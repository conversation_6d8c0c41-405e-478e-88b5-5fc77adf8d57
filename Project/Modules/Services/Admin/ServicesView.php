<?php

namespace Project\Modules\Services\Admin;

use Systemsight\Framework\Libraries\BaseEntity;
use Systemsight\Framework\Libraries\DoctrineOrm;
use Systemsight\Framework\Libraries\Registry;
use Systemsight\Framework\Libraries\Responses\AjaxFormResponse;
use Systemsight\Modules\Datasets\Models\DatasetsCity;
use Systemsight\Modules\Datasets\Models\DatasetsClass;
use Systemsight\Modules\Datasets\Models\DatasetsItemRepository;
use Systemsight\Modules\Events\Core\RegistrationStates;
use Systemsight\Modules\General\GeneralTemplates;
use Systemsight\Modules\Services\Models\ServicesItemRepository;
use Systemsight\Modules\Services\Models\ServicesParticipant;
use Systemsight\Modules\Services\Models\ServicesPeriod;
use Systemsight\Modules\Services\Models\ServicesEvent;
use Systemsight\Modules\Services\Models\ServicesEventRepository;
use Systemsight\Modules\Services\Models\ServicesReservation;
use Systemsight\Modules\Services\Models\ServicesReservationRepository;
use Systemsight\Modules\Services\Models\ServicesSituation;
use Systemsight\Modules\Services\Tools\ServicesReservationsUtilities;
use Systemsight\Modules\Services\Validators\ServicesEventDatetime;
use Systemsight\Modules\Services\Tools\ServicesCalendarUtilities;
use Systemsight\Modules\Services\Validators\ServicesEventRecurringPeriod;
use Systemsight\Modules\Services\Validators\ServicesEventTime;
use Systemsight\Modules\Services\Validators\ServicesReservationVisitTime;
use Systemsight\Modules\Simplemodule\Tools\ListingBuilder;
use Systemsight\Modules\Simplemodule\Tools\ManageBuilder;
use Systemsight\Modules\Simplemodule\Admin\SimplemoduleView;

//@fwXmlMenuGroup services, menu_group=tools, menu_icon=fa-handshake-o, priority=5
class ServicesView extends SimplemoduleView
{
    protected $mainItem;

    //region Services
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-list, priority=50
    public function listing_item()
    {
        return $this->listing('item');
    }

    //@fwXmlConfig permission=write
    public function add_item()
    {
        return $this->add('item');
    }

    //@fwXmlConfig permission=edit
    public function edit_item()
    {
        $this->addClientscript('maskedinput');
        return $this->edit('item');
    }

    /**
     * Add fields to records' list
     *
     * @param ListingBuilder $listingBuilder Item's list field builder
     * @return ListingBuilder
     */
    protected function item_listing_builder(ListingBuilder $listingBuilder): ListingBuilder
    {
        return $listingBuilder
            ->addField('photo', [
                'image' => 'images',
                'orderable' => false
            ])
            ->addField('title')
            //->addField('description', ['eval'=>'{$item->description|truncate:50:"...":true}'])
            ->addField('city', [
                'eval' => '{$item->getCityTitle()}',
                "orderable" => false
            ])
            ->addField('reservations', [
                /** @see static::reservation_listing_builder() */
                'eval' => '{if $item->serices_reservations->count()}<a href="services/listing_reservation?filter[service]={$item.id}&ff=1&_display=iframe" class="btn btn-outline-secondary btn-sm" data-fancybox data-type="iframe">{$item->serices_reservations->count()}</a>{/if}',
                /** @see ServicesItemRepository::render_reservations_ordering() */
            ])
            ->addField('created_at')

            ->removeSettingField('idle_time')

            ->addAction(ListingBuilder::$ACTION_ORDERABLE, [
                'orderable' => [
                    'field' => 'position',
                    'default_field' => true,
                    'default_direction' => 'ASC'
                ]
            ])

            ->addFilter('query', ['type' => ManageBuilder::TYPE_TEXT])
            ->addFilter('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'values' => Registry::loadRepository('Datasets.City')->getListByEntityRelation(
                    'Services.Item',
                    [],
                    'IDENTITY(item.city)',
                    'item.position ASC, item.title ASC'
                )
            ])
            ->addFilter('education_area', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.EducationArea', 'method' => 'getList']
            ])
            ->addFilter('class', [
                /** @see ServicesItemRepository::render_class_filter() */
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.Class', 'method' => 'getList'],
                'multiple' => true,
            ])
            ->addFilter('situation', [
                /** @see ServicesItemRepository::render_situation_filter() */
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Situation', 'method' => 'getFlatTree']
            ])
            ->addFilter('with_reservations', [
                /** @see ServicesItemRepository::render_with_reservations_filter() */
                'type' => ManageBuilder::TYPE_CHECKBOX,
                'render_label' => true,
                'label' => 'services:with_reservations',
                'i18n_title' => 'yes',
            ])
        ;
    }

    /**
     * Record's editing fields
     *
     * @param ManageBuilder $manageBuilder
     * @return ManageBuilder
     */
    protected function item_manage_builder(ManageBuilder $manageBuilder): ManageBuilder
    {
        $currentUser = Registry::loadRepository('Administrators.User')->getCurrentUser();

        $manageBuilder
            ->addTab('messages', ['icon' => 'fa fa-file-text-o'])
            ->addTab('media', ['label' => 'media', 'icon' => 'fa fa-file-image-o'])
            ->addTab('documents', ['label' => 'documents', 'icon' => 'fa fa-file-o'])
            ->addTitleSlug()
            ->addPosition()
            ->addVisible(['default_value' => true])
            ->addField('description', ['type' => ManageBuilder::TYPE_WYSIWYG, 'tab' => 'messages'])
            ->addField('duration', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'tooltip' => 'min.'
            ])
            ->addField('color', ['type' => ManageBuilder::TYPE_COLORPICKER, 'sidebar' => true])
            ->addField('price', ['type' => ManageBuilder::TYPE_MONEY, 'sidebar' => true])
            ->addField('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Datasets.City',
                //'loadValues' => ['repository' => 'Datasets.City', 'method' => 'getList'],
                'loadValues' => [
                    'repository' => 'Administrators.User',
                    'method' => 'getUserCities',
                    'params' => [null, false]
                ],
                'validation' => 'not_blank',
                'id' => 'city'
            ])
            ->addField('education_area', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Datasets.EducationArea',
                'loadValues' => ['repository' => 'Datasets.EducationArea', 'method' => 'getList'],
                'validation' => 'not_blank',
            ])
            ->addField('class', [
                'type' => ManageBuilder::TYPE_RELATION,
                'label' => 'services: class',
                'entity' => 'Datasets.Class',
                'values_method' => 'getTree',
                'validation' => 'not_blank',
                'multiple' => true,
            ])
            ->addField('situation', [
                'type' => ManageBuilder::TYPE_RELATION,
                'entity' => 'Services.Situation',
                'values_method' => 'getTree',
                'validation' => 'not_blank',
                //'multiple' => true,
            ])
        ;

        /*
         * STEAM-107: naikinamas sio lauko priskyrimas
        if($currentUser->isSuperUser() || Registry::$controller->canPerform('Administrators', 'edit_user')) {
            $manageBuilder->addField('administrator', [
                'type' => ManageBuilder::TYPE_RELATION,
                'entity' => 'Administrators.User',
                'values' => [],
                'input_options' => [
                    'data_url' => 'administrators/ajax_get_users_by_city',
                    'depends_on' => [
                        'city'
                    ],
                ],
                'multiple' => true,
            ]);
        }
        */

        $manageBuilder
            ->addMedia('Services.ItemImages', ['tab' => 'media'])
            ->addMedia('Services.ItemDocument', ['tab' => 'documents'])
        ;

        return $manageBuilder;
    }

    //@fwXmlConfig permission=edit
    public function toggle_item()
    {
        return parent::toggle('item');
    }

    //@fwXmlConfig permission=edit
    public function position_item()
    {
        return parent::position('item');
    }

    //@fwXmlConfig permission=delete
    public function delete_item()
    {
        $unDeletableItemsTitle = [];
        $deletable_items = [];
        $ids = $this->_getIntArray('id');

        foreach ($ids as $id) {
            if ($item = $this->getRepository()->getOne(['id' => $id])) {
                if ($item->getEventsCount() > 0) {
                    $unDeletableItemsTitle[] = $item->get('title');
                } else {
                    $deletable_items[] = $id;
                }
            }
        }

        if (!empty($unDeletableItemsTitle)) {
            $this->_error(
                'literal:'
                . Registry::l10n()->get('services: these items have events and cannot be deleted')
                . ': '
                . implode(', ', $unDeletableItemsTitle)
            );
        }

        $this->request->setParameter('id', $deletable_items);

        return parent::delete('item');
    }
    //endregion Services


    //region Events
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-newspaper-o, priority=40
    public function listing_event()
    {
        return $this->listing('event');
    }

    //@fwXmlConfig permission=read
    public function preview_event()
    {
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);

        /** @var ServicesEvent|null $item */
        if ($id = $this->_getInt('id') and $item = $this->getRepository()->find($id)) {
            $token = Registry::csrfManager()->getToken();
            $this->_set('item', $item);
            $this->_set('token', $token->getValue());
        } else {
            return $this->_render('services.not_found.event.tpl');
        }

        if (
            $datetime = $this->_getDateTime('datetime')
            and $this->getRepository()->isValidVisitTime($item, $datetime)
        ) {
            $this->_set('reservation', Registry::loadRepository('Services.Reservation')->getEventReservation($item, $datetime));
            $this->_set('selectedDatetime', $datetime);
            $this->_set('dayInactive', (int) (bool) Registry::loadRepository('Services.EventDayInactive')->isDateInactive($item, $datetime));
        }

        if (!empty($item->by_lab)) {
            $services = $item->getServices();
            $this->_set('services', $services);

            return $this->_render('services.preview_event.situation.tpl');
        } else {
            return $this->_render('services.preview_event.tpl');
        }
    }

    /**
     * Į čia ateina AJAX iš kalendoriaus.
     * @return false|string
     */
    public function toggle_event_date()
    {
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_EMPTY);
        $serviceEventId = Registry::request()->getInt('id');
        /** @var ServicesEvent|null $item */
        $item = Registry::loadRepository('Services.Event')->find($serviceEventId);
        $datetime = Registry::request()->getString('datetime');
        if (Registry::loadRepository('Services.Event')->isValidVisitTime($item, $datetime)) {
            $servicesEventDayInactive = Registry::loadRepository('Services.EventDayInactive')->isDateInactive($item, $datetime);
            if ($servicesEventDayInactive) {
                Registry::loadRepository('Services.EventDayInactive')->deleteByIds($servicesEventDayInactive->getId());
                return $this->_json([
                    'status' => 'activated',
                ]);
            } else {
                Registry::loadRepository('Services.EventDayInactive')->disableDay($item, $datetime);
                return $this->_json([
                    'status' => 'disabled',
                ]);
            }
        }
        return $this->_json([
            'status' => 'invalid',
        ]);
    }

    public function update_event_datefrom()
    {
        $this->_itemName = 'event';
        $result = [
            'error' => '',
            'status' => ''
        ];

        /** @var ServicesEventRepository $repository */
        $repository = $this->getRepository();

        /** @var ServicesEvent $item */
        if (
            $this->_isPost()
            and $this->_validateToken(false)
            and $id = $this->_getInt('id')
            and $item = $repository->find($id)
        ) {
            $post_data = $this->_get();
            $error = $repository->validateUpdateDate($post_data, $item);

            if (empty($error)) {
                $item->datetime = new \DateTime($post_data['date_start']);
                $repository->save($item);
                //TODO: jei bus rezervaciju, joms pranesti apie pasikeitusi laika
                //$repository->sendReservationTimeChanged($item);

                $result['status'] = 'OK';
                $result['success'] = Registry::l10n()->get('changes saved');
            } else {
                $result['error'] = Registry::l10n()->get($error);
            }
        }

        return $this->_json($result);
    }

    //@fwXmlConfig permission=write
    public function add_event()
    {
        $this->add('event');
    }

    //@fwXmlConfig permission=edit
    public function edit_event()
    {
        if ($this->_isIframe()) {
            $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);
            if (
                $this->_isPost()
                && in_array($this->_getString('action'), [ManageBuilder::FORM_SAVE, ManageBuilder::FORM_SAVE_AND_CLOSE])
            ) {
                Registry::session()->set('reload_after', true);
            }
        }

        return $this->edit('event');
    }

    //@fwXmlConfig permission=delete
    public function delete_event()
    {
        $unDeletableItemsTitle = [];
        $deletable_items = [];
        $ids = $this->_getIntArray('id');

        foreach ($ids as $id) {
            /** @var ServicesEvent $item */
            if ($item = $this->getRepository()->getOne(['id' => $id])) {
                if (Registry::loadRepository('Services.Reservation')->count(['event' => $item]) > 0) {
                    $unDeletableItemsTitle[] = '#' . $item->getId() . ': ' . $item->getDatetimeFormatted();
                } else {
                    $deletable_items[] = $id;
                }
            }
        }

        if (!empty($unDeletableItemsTitle)) {
            $this->_error(
                'literal:'
                . Registry::l10n()->get('services: these activities have reservations and cannot be deleted')
                . ': '
                . implode(', ', $unDeletableItemsTitle)
            );
        }

        $this->request->setParameter('id', $deletable_items);

        if ($this->_isIframe()) {
            $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);
            Registry::session()->set('reload_after', true);
            parent::delete('event', false);

            return $this->closefancybox();
        } else {
            return parent::delete('event');
        }
    }

    //@fwXmlConfig permission=read
    public function ajax_get_services_by_city()
    {
        $items = [];

        if ($cityId = $this->_getIntArray('city')) {
            $items = Registry::loadRepository('Services.Item')->getList([
                'city' => $cityId,
            ]);
            $items = array_map(function ($item) {
                return ['id' => $item->getId(), 'title' => $item->get('title')];
            }, $items);
        }

        return $this->_json($items);
    }

    //@fwXmlConfig permission=read
    public function ajax_get_services_by_situation()
    {
        $items = [];
        if (
            $cityId = $this->_getIntArray('city')
            and $situationId = $this->_getIntArray('service_situation')
        ) {
            $items = Registry::loadRepository('Services.Item')->getList([
                'city' => $cityId,
                'relations' => ['Services.Situation' => $situationId],
            ]);
            $items = array_map(function ($item) {
                return ['id' => $item->getId(), 'title' => $item->get('title'), 'selected' => true];
            }, $items);
        }

        return $this->_json($items);
    }



    /**
     * @param ListingBuilder $listingBuilder reservation's list field builder
     * @return ListingBuilder
     */
    protected function event_listing_builder(ListingBuilder $listingBuilder): ListingBuilder
    {
        return $listingBuilder
            ->removeAction(ListingBuilder::$ACTION_ORDERABLE)
            ->addAction(ListingBuilder::$ACTION_TED, ['skip_toggle' => true, 'orderable' => false, "th_class" => "x2"])
            ->addField('datetime', [
                'include' => 'inc/event.datetime.tpl',
                'label' => 'activity time',
                'orderable' => true
            ])
            ->addField('situation', [
                'eval' => '{$item->getSituationTitle()}',
                'orderable' => false,
            ])
            ->addField('service', [
                'include' => 'inc/event.services.tpl',
                //'eval' => '{"<br>"|@implode:$item->getRelationItemTitles("Services.Item")}',
                'orderable' => false,
            ])
            ->addField('city', [
                'eval' => '{$item->getCityTitle()}',
                "orderable" => false
            ])
            ->addField('created_at', [
                'orderable' => [
                    'default_field' => true,
                    'default_direction' => 'DESC'
                ],
            ])

            ->addSettingField('event_time', [
                'label' => 'services:event_time',
                'date_format' => 'H:i',
            ])
            ->addSettingField('days_of_week', [
                'label' => 'services:days_of_week',
                'eval' => '{$item->getPrintableDaysOfWeek()}',
                'orderable' => false,
            ])
            ->addSettingField('recurring_from', [
                'label' => 'services:recurring_from',
                'date_format' => 'Y-m-d',
            ])
            ->addSettingField('recurring_from', [
                'label' => 'services:recurring_to',
                'date_format' => 'Y-m-d',
            ])
            ->addSettingField('register_from', [
                'date_format' => 'Y-m-d',
            ])
            ->addSettingField('register_to', [
                'date_format' => 'Y-m-d',
            ])

            ->addFilter('activity_date', [
                'type' => ManageBuilder::TYPE_DATERANGE,
                'label' => 'services: activity date'
            ])
            ->addFilter('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'values' => Registry::loadRepository('Datasets.City')->getListByEntityRelation(
                    'Services.Item',
                    [],
                    'IDENTITY(item.city)',
                    'item.position ASC, item.title ASC'
                )
            ])
            ->addFilter('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Item', 'method' => 'getList']
            ])
            ->addFilter('situation', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Situation', 'method' => 'getFlatTree']
            ])
            ->addFilter('education_area', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.EducationArea', 'method' => 'getList']
            ])
            ->addFilter('class', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.Class', 'method' => 'getList', 'params' => [['order' => 'item.position ASC']]],
                'multiple' => true,
            ])
            ->removeFormAction('position');
    }

    /**
     * Record's editing fields
     *
     * @param ManageBuilder $manageBuilder
     * @return ManageBuilder
     */
    protected function event_manage_builder(ManageBuilder $manageBuilder): ManageBuilder
    {
        $manageBuilder
            ->addField('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Datasets.City',
                'loadValues' => [
                    'repository' => 'Administrators.User',
                    'method' => 'getUserCities',
                    'params' => [null, false]
                ],
                'validation' => 'not_blank',
                'id' => 'city'
            ])

            ->addField('by_lab', [
                'type' => ManageBuilder::TYPE_RADIOSELECT,
                'label' => 'services:by_lab',
                'values' => [
                    ['id' => 0, 'i18n_title' => 'services:by activity'],
                    ['id' => 1, 'i18n_title' => 'services:by laboratory'],
                ],
                'value' => !is_null($manageBuilder->entity->by_lab) ? (int) $manageBuilder->entity->by_lab : null,
            ])
            ->addField('service', [
                'type' => ManageBuilder::TYPE_RELATION,
                'entity' => 'Services.Item',
                'input_options' => [
                    /** @see static::ajax_get_services_by_city() */
                    'data_url' => 'services/ajax_get_services_by_city',
                    'depends_on' => [
                        'city'
                    ],
                ],
                'validation' => ['not_blank'],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'by_lab',
                        'eq' => '0'
                    ]
                ],



                'id' => 'custom_service',

            ])



            // ->addField('services_select_all', [
            //     'type' => ManageBuilder::TYPE_CHECKBOX,
            //     'id' => 'services_select_all',

            // ])



            ->addField('situation', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Services.Situation',
                'loadValues' => ['repository' => 'Services.Situation', 'method' => 'getList'],
                'validation' => 'not_blank',
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'by_lab',
                        'eq' => '1'
                    ]
                ],
                'id' => 'service_situation',
            ])
            ->addField('services', [
                'type' => ManageBuilder::TYPE_RELATION,
                'label' => 'services: activities',
                'entity' => 'Services.Item',
                'input_options' => [
                    /** @see static::ajax_get_services_by_situation() */
                    'data_url' => 'services/ajax_get_services_by_situation',
                    'depends_on' => [
                        'city',
                        'service_situation'
                    ],
                ],
                'validation' => ['not_blank'],
                'multiple' => true,
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'by_lab',
                        'eq' => '1'
                    ]
                ],
            ])

            ->addField('recurring', [
                'type' => ManageBuilder::TYPE_RADIOSELECT,
                'label' => 'services:recurring',
                'values' => [
                    ['id' => 0, 'i18n_title' => 'no'],
                    ['id' => 1, 'i18n_title' => 'yes'],
                ],
                'value' => !is_null($manageBuilder->entity->recurring) ? (int) $manageBuilder->entity->recurring : null,
            ])

            ->addField('datetime', [
                'type' => ManageBuilder::TYPE_DATETIME,
                'label' => 'visit time',
                'input_options' => [
                    'minDate' => $manageBuilder->entity->getId() ? null : date('Y-m-d'),
                    'stepMinute' => 15
                ],
                'nullable' => true,
                'allow_clear' => true,
                'validation' => [
                    'not_blank',
                    new ServicesEventDatetime(['skip_range' => true, 'event' => $manageBuilder->entity])
                ],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'recurring',
                        'eq' => '0'
                    ]
                ],
                //'col' => 'col-md-6',
            ])
            ->addField('event_time', [
                'type' => ManageBuilder::TYPE_TIME,
                'label' => 'services:event_time',
                'nullable' => true,
                'allow_clear' => true,
                'validation' => [
                    'not_blank',
                    new ServicesEventTime(['skip_range' => true, 'event' => $manageBuilder->entity])
                ],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'recurring',
                        'eq' => '1'
                    ]
                ],
                'col' => 'col-md-6',
            ])
            ->addField('days_of_week', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'services:days_of_week',
                'values' => array_map(function ($d) {
                    return ['id' => $d, 'i18n_title' => "global week day {$d}"];
                }, array_merge(\range(1, 6), [0])),
                'multiple' => true,
                'validation' => ['not_blank'],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'recurring',
                        'eq' => '1'
                    ]
                ],
                'col' => 'col-md-6',
            ])
            ->addField('recurring_from', [
                'type' => ManageBuilder::TYPE_DATE,
                'label' => 'services:recurring_from',
                'input_options' => [
                    'minDate' => $manageBuilder->entity->getId() ? null : date('Y-m-d'),
                ],
                'nullable' => true,
                'allow_clear' => true,
                'validation' => [
                    'not_blank',
                    new ServicesEventRecurringPeriod(['skip_range' => true, 'event' => $manageBuilder->entity])
                ],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'recurring',
                        'eq' => '1'
                    ]
                ],
                'col' => 'col-md-6',
            ])
            ->addField('recurring_to', [
                'type' => ManageBuilder::TYPE_DATE,
                'label' => 'services:recurring_to',
                'input_options' => [
                    'minDate' => $manageBuilder->entity->getId() ? null : date('Y-m-d'),
                ],
                'nullable' => true,
                'allow_clear' => true,
                'validation' => [
                    'not_blank',
                ],
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'recurring',
                        'eq' => '1'
                    ]
                ],
                'col' => 'col-md-6',
            ])

            ->addField("register_from", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d'),
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField("register_to", [
                'type' => ManageBuilder::TYPE_DATE,
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])

            ->addField('max_participants', [
                'type' => ManageBuilder::TYPE_SPINNER,
                'comment' => '0 - neribojama',
                //'col' => 'col-sm-6',
            ])
            ->addTab('service_reservation', [
                'label' => 'services/listing_reservation',
            ])
            /** @see ServicesEvent::$service_reservation */
            ->addAssociationManagement('service_reservation', ['tab' => 'service_reservation'])
        ;

        // echo "<pre>";
        // print_r($manageBuilder);
        // echo "</pre>";

        return $manageBuilder;
    }
    //endregion


    //region Calendar
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-calendar, priority=40
    public function calendar()
    {
        $this->_itemName = 'event';
        $this->addClientScript('fullcalendar-cms');

        $listingBuilder = $this->getListingBuilder($this->_itemName);
        $listingBuilder->build();

        $filter = $this->_getFilter();

        $businessHours = ServicesCalendarUtilities::getBusinessHoursSettings($filter);

        $this->_set('events', []);
        $this->_set('filter', $filter);
        $this->_set('listingBuilder', $listingBuilder);
        $this->_set('businessHours', $businessHours);
        $this->_set('calendarType', 'events');
        /** @see static::loadCalendarEvents() iš ten kraus kalendoriaus įrašus */
        $this->_set('loadEventsUri', '/admin/' . $this->request->getLanguage() . '/services/loadCalendarEvents');

        return $this->_render('services.calendar.tpl');
    }

    //@fwXmlConfig permission=read
    public function print_calendar()
    {
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_EMPTY);

        if ($this->_isPost() and $html = $this->_get('html')) {
            $this->_set('content', $html);
        }

        return $this->_render('services.print_calendar.tpl');
    }

    //@fwXmlConfig permission=read, menu=1, icon=fa fa-calendar, priority=35
    public function reservations_calendar()
    {
        $this->_itemName = 'reservation';
        $this->addClientScript('fullcalendar-cms');

        $listingBuilder = $this->getListingBuilder($this->_itemName);
        $listingBuilder->build();

        $filter = $this->_getFilter();

        $businessHours = ServicesCalendarUtilities::getBusinessHoursSettings($filter);

        $this->_set('events', []);
        $this->_set('filter', $filter);
        $this->_set('expandFilters', !empty($filter));
        $this->_set('listingBuilder', $listingBuilder);
        $this->_set('businessHours', $businessHours);
        /** @see static::loadCalendarReservationsEvents() */
        $this->_set('loadEventsUri', '/admin/' . $this->request->getLanguage() . '/services/loadCalendarReservationsEvents');

        return $this->_render('services.calendar.tpl');
    }

    /**
     * Į čia ateina AJAX. Uzkrauna Services.Event irasus kalendoriui pagal rodoma menesi/savaite/diena realiu laiku
     *
     * @return false|string
     */
    public function loadCalendarEvents()
    {
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_AJAX);

        $events = [];

        try {
            if (
                $start = substr($this->_getString('start'), 0, 10)
                and $end = substr($this->_getString('end'), 0, 10)
                and $dateStart = new \DateTime($start)
                and $dateEnd = new \DateTime($end)
            ) {
                $this->_itemName = 'event';
                $filter = $this->_getFilter();
                if (empty($filter['activity_date'])) {
                    $filter['activity_date'] = [
                        'from' => $dateStart->format('Y-m-d'),
                        'to' => $dateEnd->format('Y-m-d'),
                    ];
                }

                /** @var ServicesEventRepository $repository */
                $repository = Registry::loadRepository('Services.Event');

                $events = $repository->getEventsList($filter);
                $daysOff = ServicesCalendarUtilities::getDaysOffSettings($filter);
                $events = array_merge($events, $daysOff);
            }
        } catch (\Exception $exception) {
        }

        return $this->_json($events);
    }

    /**
     * Uzkrauna Services.Reservation irasus kalendoriui pagal rodoma menesi/savaite/diena realiu laiku
     *
     * @return false|string
     */
    public function loadCalendarReservationsEvents()
    {
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_AJAX);

        $events = [];

        try {
            if (
                $start = substr($this->_getString('start'), 0, 10)
                and $end = substr($this->_getString('end'), 0, 10)
                and $dateStart = new \DateTime($start)
                and $dateEnd = new \DateTime($end)
            ) {
                $this->_itemName = 'reservation';
                $filter = $this->_getFilter();
                /** @see ServicesReservationRepository::render_visit_time_filter() */
                if (empty($filter['visit_time'])) {
                    $filter['visit_time'] = [
                        'from' => $dateStart->format('Y-m-d'),
                        'to' => $dateEnd->format('Y-m-d'),
                    ];
                }

                /** @var ServicesReservationRepository $repository */
                $repository = Registry::loadRepository('Services.Reservation');

                $events = $repository->getEventsList($filter);
                $daysOff = ServicesCalendarUtilities::getDaysOffSettings($filter);
                $events = array_merge($events, $daysOff);
            }
        } catch (\Exception $exception) {
        }

        return $this->_json($events);
    }
    //endregion Calendar


    //region Reservations
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-edit, priority=30
    public function listing_reservation()
    {
        return $this->listing('reservation');
    }

    //@fwXmlConfig permission=read
    public function preview_reservation()
    {
        if ($id = $this->_getInt('id') and $item = $this->getRepository()->getOne(['id' => $id])) {
            $token = Registry::csrfManager()->getToken();
            $possibleTransitions = $item->getSM()->getPossibleTransitions();
            $this->_set('item', $item);
            $this->_set('hasPossibleTransitions', !empty($possibleTransitions));
            $this->_set('token', $token->getValue());
        }

        $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);

        return $this->_render();
    }

    //@fwXmlConfig permission=write
    public function add_reservation()
    {
        $this->add('reservation');
    }

    //@fwXmlConfig permission=edit
    public function edit_reservation()
    {
        return $this->edit('reservation');
    }

    //@fwXmlConfig permission=delete
    public function delete_reservation()
    {
        return parent::delete('reservation');
    }

    //@fwXmlConfig permission=read
    public function ajax_get_events()
    {
        $items = [];
        if ($serviceId = $this->_getInt('service_item')) {
            $items = Registry::loadRepository('Services.Event')->getSelectList([
                'service' => $serviceId,
            ]);
        }

        return $this->_json($items);
    }

    //@fwXmlConfig permission=read
    public function ajax_get_classes()
    {
        $items = [];
        if (
            $serviceId = $this->_getInt('service_item')
            and $service = Registry::loadRepository('Services.Item')->find($serviceId)
        ) {
            $items = Registry::loadRepository('Relations.Item')->getEntityRelations($service, 'Datasets.Class');
            $items = array_map(function ($item) {
                return ['id' => $item->getId(), 'title' => $item->get('title')];
            }, $items);
        }

        return $this->_json($items);
    }

    //@fwXmlConfig permission=read
    public function ajax_get_visit_times()
    {
        $items = [];
        /** @var ServicesEvent $event */
        if (
            $eventId = $this->_getInt('service_event')
            and $event = Registry::loadRepository('Services.Event')->find($eventId)
        ) {
            $items = Registry::loadRepository('Services.Event')->generateEventPosibleTimesSelect($event);
        }

        return $this->_json($items);
    }

    /**
     * Add fields to records' list
     *
     * @param ListingBuilder $listingBuilder reservation's list field builder
     * @return ListingBuilder
     */
    protected function reservation_listing_builder(ListingBuilder $listingBuilder): ListingBuilder
    {

        $participantsGlue = $this->_getString('export') ? '; ' : '<br>';
        $listingBuilder
            ->removeAction(ListingBuilder::$ACTION_ORDERABLE)
            ->addAction(ListingBuilder::$ACTION_TED, ['skip_toggle' => true, 'orderable' => false, "th_class" => "x2"])
            ->addField('visit_time', [
                'label' => 'visit time',
                'orderable' => true
            ])
            ->addField('steam_center', [
                'label' => 'steam_center',
                'eval' => '{$item->getSteamCenterTitle()}',
                'orderable' => false
            ])
            ->addField('laboratory', [
                'label' => 'situation',
                'eval' => '{$item->getLaboratoryTitle()}',
                'orderable' => false
            ])
            ->addField('service', [
                'eval' => '{if $service = $item->getService()}'
                    . '<a href="services/edit_item/?id={$service->getId()}">{if !empty($service->get("title"))}{$service->get("title")}{else}<strong style="color:red;">{$localization->get("no_label")}</strong>{/if}</a>'
                    . '{/if}',
                'orderable' => false
            ])
            ->addField('user', [
                'type' => 'html',
                'eval' => '{$item->getFullName()}',
                'orderable' => [
                    'field' => 'user_fullname'
                ]
            ])
            ->addField('status', [
                'type' => 'html',
                'eval' => '{if !empty($item->status)}{$item->getStatusTitle()}{/if}',
            ])
            ->addField('created_at', [
                'orderable' => [
                    'default_field' => true,
                    'default_direction' => 'DESC'
                ],
            ])

            //Default galimu lauku rikiavimas ir label priskyrimas
            ->addSettingField('id')
            ->addSettingField('persons', [
                'label' => 'services: number of visitors',
                'eval' => '{$item->getParticipantsCount()}',
            ]);
        if ($this->_getString('export')) {
            $listingBuilder
                ->addSettingField('participants', [
                    'label' => 'services: pupils',
                    /** @see ServicesReservation::getParticipantsListToString() */
                    'eval' => '{$item->getParticipantsListToString("' . $participantsGlue . '")}',
                ]);
        }
        $listingBuilder
            ->addSettingField('comment', [
                'label' => 'comment',
            ])

            ->removeSettingField('user_hash')

            ->addFilter('query', ['type' => ManageBuilder::TYPE_TEXT])
            ->addFilter('visit_time', [
                /** @see ServicesReservationRepository::render_visit_time_filter() */
                'type' => ManageBuilder::TYPE_DATETIMERANGE,
                'label' => 'visit date',
            ])
            ->addFilter('created_at', [
                'type' => ManageBuilder::TYPE_DATETIMERANGE,
            ])
            ->addFilter('year', [
                /** @see ServicesReservationRepository::render_year_filter() */
                'type' => ManageBuilder::TYPE_SELECT2,
                'values' => Registry::loadRepository('Services.Reservation')->getVisitYearsForFilter(),
                'label' => 'literal:Veiklos metai',
            ])
            ->addFilter('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => [
                    'repository' => 'Administrators.User',
                    'method' => 'getUserCities',
                    'params' => [null, false],
                ]
            ])
            ->addFilter('education_area', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.EducationArea', 'method' => 'getList'],
            ])
            ->addFilter('class', [
                'type' => 'relations.relationFilter',
                'entity' => 'Datasets.Class',
            ])
            ->addFilter('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Item', 'method' => 'getList'],
            ])
            ->addFilter('situation', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Situation', 'method' => 'getFlatTree'],
            ])
            ->addFilter('status', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.Reservation', 'method' => 'getStatusSelectList'],
            ])
            ->addFilter('event', [
                'type' => ManageBuilder::TYPE_SELECT2_AJAX,
                'loadValues' => ['repository' => 'Services.Event', 'method' => 'getList'],
                'entity' => 'Services.Event',
                'label' => 'services/listing_event',
            ])
            ->addFilter('user', [
                'type' => 'select2_ajax',
                'entity' => 'Users.Item',
                'ajax' => true,
            ])
            ->removeFormAction('position');

        return $listingBuilder;
    }

    /**
     * Record's editing fields
     *
     * @param ManageBuilder $manageBuilder
     * @return ManageBuilder
     */
    protected function reservation_manage_builder(ManageBuilder $manageBuilder): ManageBuilder
    {
        $service = $manageBuilder->entity->getId() ? $manageBuilder->entity->getService() : null;

        $invoiceConditionals = [
            [
                'show' => true,
                'when' => 'invoice_required',
                'eq' => true
            ]
        ];

        $manageBuilder
            ->addField('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Services.Item',
                'loadValues' => [
                    'repository' => 'Services.Item',
                    'method' => 'getList'
                ],
                //'value' => $service,
                'validation' => ['not_blank'],
                'id' => 'service_item',
            ])
            ->addField('event', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'services: event',
                'entity' => 'Services.Event',
                'values' => [],
                'input_options' => [
                    /** @see static::ajax_get_events() */
                    'data_url' => 'services/ajax_get_events',
                    'depends_on' => [
                        'service_item'
                    ],
                ],
                'validation' => ['not_blank'],
                'id' => 'service_event',
                'col' => 'col-sm-6',
            ])
            ->addField('visit_time', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'visit time',
                'values' => [],
                'value' => ($manageBuilder->entity->getId() && !empty($manageBuilder->entity->visit_time))
                    ? $manageBuilder->entity->visit_time->format('Y-m-d H:i:s') : null,
                'input_options' => [
                    /** @see static::ajax_get_visit_times() */
                    'data_url' => 'services/ajax_get_visit_times',
                    'depends_on' => [
                        'service_event'
                    ],
                ],
                'selectFirst' => true,
                'validation' => [
                    'not_blank',
                    new ServicesReservationVisitTime(['skip_range' => true, 'reservation' => $manageBuilder->entity]),
                ],
                'col' => 'col-sm-6',
            ])
            ->addField('user', [
                'type' => 'select2_ajax',
                'entity' => 'Users.Item',
            ])

            ->addField('first_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'teacher first name',
                'validation' => ['not_blank'],
                'col' => 'col-sm-6'
            ])
            ->addField('last_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'teacher last name',
                'validation' => ['not_blank'],
                'col' => 'col-sm-6'
            ])
            ->addField('municipality', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'services: municipality',
                'entity' => 'Datasets.Municipality',
                'loadValues' => ['repository' => 'Datasets.Municipality', 'method' => 'getList'],
                'validation' => 'not_blank',
                //'col' => 'col-sm-6',
            ])
            ->addField('institution_title', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'services: educational institution title',
                'validation' => 'not_blank',
                'col' => 'col-sm-6',
            ])
            ->addField('taught_subject', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'services: taught subject',
                'validation' => 'not_blank',
                'col' => 'col-sm-6',
            ])
            ->addField('class', [
                'type' => ManageBuilder::TYPE_RELATION,
                'label' => 'services: class',
                'entity' => 'Datasets.Class',
                //'loadValues' => ['repository' => 'Datasets.Class', 'method' => 'getList'],
                'input_options' => [
                    /** @see static::ajax_get_classes() */
                    'data_url' => 'services/ajax_get_classes',
                    'depends_on' => [
                        'service_item'
                    ],
                ],
                'validation' => 'not_blank',
                'multiple' => true,
            ])
            ->addField('phone', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'contact phone',
                'value' => $user->phone ?? null,
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField('email', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'contact email',
                'value' => !empty($user) ? $user->getEmail() : null,
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField('comment', [
                'type' => ManageBuilder::TYPE_TEXTAREA,
                'label' => 'comment',
            ])

            ->addField('service_price', [
                'type' => ManageBuilder::TYPE_MONEY,
                'sidebar' => true,
            ])

            ->addTab('invoice', ['label' => 'services: invoice', 'icon' => 'fa-file-pdf-o'])
            ->addField('invoice_required', [
                'type' => ManageBuilder::TYPE_CHECKBOX,
                'id' => 'invoice_required',
                'tab' => 'invoice',
            ])
            ->addField('buyer_title', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_code', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_vat_code', [
                'type' => ManageBuilder::TYPE_TEXT,
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_address', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])

            ->addTab('participants', ['label' => 'services: pupils', 'icon' => 'fa-users'])
            /** @see static::listing_participant() */
            /** @see static::participant_listing_builder() */
            ->addAssociationManagement('participants', ['tab' => 'participants'])
        ;

        if ($manageBuilder->entity instanceof ServicesReservation && $manageBuilder->entity->getId()) {
            $manageBuilder->addField('change_status', [
                'type' => ManageBuilder::TYPE_INCLUDE,//žr. /Services/Admin/templates/inc/change_status.tpl
                'render_label' => false,
                'sidebar' => true
            ]);

            $manageBuilder->addField('link', [
                'type' => ManageBuilder::TYPE_EVAL,
                'eval' => '<a href="{$seo->getBaseModuleStructureAlias("Services/reservation")}/hash.{$item->hash}" target="_blank" class="btn btn-link px-0">{$l10n->get("link")} <i class="fa fa-external-link" aria-hidden="true"></i></a>',
                'render_label' => false,
                'sidebar' => true
            ]);
        }

        return $manageBuilder;
    }

    /**
     * Į čia ateina AJAX iš `services/listing_reservation`
     */
    public function change_participant_has_arrived()
    {
        $participant_id = Registry::request()->getInt('participant_id');
        /** @var ServicesParticipant|null $servicesParticipant */
        $servicesParticipant = Registry::loadRepository('Services.Participant')->getOne(['id' => $participant_id]);
        $servicesParticipant->has_arrived = !$servicesParticipant->has_arrived;
        DoctrineOrm::$entityManager->persist($servicesParticipant);
        DoctrineOrm::$entityManager->flush();
        return $this->_json([
            'success' => true,
        ]);
    }
    //endregion Reservations


    //region Participants
    //@fwXmlConfig permission=read
    public function listing_participant()
    {
        if ($assocData = $this->getAssociationParentData()) {
            $reservation = $assocData->getEntity();
        }

        if (empty($reservation)) {
            $this->_error('services: missing reservation');

            if ($this->_isIframe()) {
                $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);
            } elseif ($this->_isAjax()) {
                $this->setMainTemplate(GeneralTemplates::TEMPLATE_AJAX);
            }

            return '';
        }

        return parent::listing('participant');
    }

    //@fwXmlConfig permission=write
    public function add_participant()
    {
        if ($assocData = $this->getAssociationParentData()) {
            /** @var ServicesReservation $reservation */
            $reservation = $assocData->getEntity();
        }
        if (empty($reservation)) {
            $this->_error('services: missing reservation');
            if ($this->_isIframe()) {
                return $this->closefancybox();
            } else {
                $this->_back();
            }
        } elseif (
            $event = $reservation->getEvent() and !empty($event->max_participants)
            and $event->max_participants <= $reservation->getParticipantsCount()
        ) {
            $this->_notification('literal:' . Registry::l10n(
                'services: maximum number of participants has been reached',
                ['limit' => $event->max_participants]
            ));
            if ($this->_isIframe()) {
                return $this->closefancybox();
            } else {
                $this->_back();
            }
        }

        parent::add('participant');
    }

    //@fwXmlConfig permission=edit
    public function edit_participant()
    {
        return parent::edit('participant');
    }

    //@fwXmlConfig permission=delete
    public function delete_participant()
    {
        return parent::delete('participant');
    }

    /**
     * @param ListingBuilder $listingBuilder
     * @return ListingBuilder
     */
    protected function participant_listing_builder(ListingBuilder $listingBuilder): ListingBuilder
    {
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_POSITION);

        $listingBuilder->addFields([
            'full_name' => [
                'eval' => '{$item->first_name} {$item->last_name}',
                'orderable' => [
                    'field' => 'full_name',
                    'default_field' => true,
                    'default_direction' => 'ASC'
                ]
            ],
            'steam_center' => [
                'label' => 'STEAM centras',
                'eval' => '{$item->getSteamCenterTitle()}',
                'orderable' => false
            ],
            'laboratory' => [
                'label' => 'Laboratorija',
                'eval' => '{$item->getLaboratoryTitle()}',
                'orderable' => false
            ],
            'has_arrived' => [
                'label' => 'services: generate certificate',
                'include' => 'inc/services.participants.has_arrived.tpl',
            ],
            'created_at' => []
        ]);

        $listingBuilder->addAction('certificate', [
            /** @See static::certificate() */
            'orderable' => false
        ]);
        $listingBuilder->addFormAction('certificates', [
            /** @see static::certificates_participant() */
            'icon' => 'fa fa-file-pdf-o',
            'mass' => true,
            'label' => 'services: generate certificate',
        ]);

        return $listingBuilder;
    }

    protected function participant_manage_builder(ManageBuilder $manageBuilder): ManageBuilder
    {
        $manageBuilder
            ->addField('first_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => ['not_blank'],
                'col' => 'col-sm-6',
            ])
            ->addField('last_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => ['not_blank'],
                'col' => 'col-sm-6',
            ])
            ->addField('has_arrived', [
                'type' => ManageBuilder::TYPE_CHECKBOX,
                'label' => 'services: generate certificate',
                'sidebar' => true,
            ])
        ;

        return $manageBuilder;
    }

    public function certificates_participant()
    {
        $ids = Registry::request()->getIntArray('id');

        /** @var ServicesParticipant[] $servicesParticipants */
        $servicesParticipants = Registry::loadRepository('Services.Participant')->getList(['id' => $ids]);
        foreach ($servicesParticipants as $serviceParticipant) {
            $serviceParticipant->has_arrived = true;
            DoctrineOrm::$entityManager->persist($serviceParticipant);
        }
        DoctrineOrm::$entityManager->flush();
        return $this->_back();
    }
    //endregion Participats


    //region States
    //@fwXmlConfig permission=write
    public function change_status_reservation()
    {
        $isIframe = $this->_isIframe() || $this->_get('iframe');

        $reservationsRepo = Registry::loadRepository('Services.Reservation');

        /** @var ServicesReservation $reservation */
        if (!($id = $this->_getInt('id') and $reservation = $reservationsRepo->find($id))) {
            if ($isIframe) {
                return $this->closefancybox();
            } else {
                $this->_back();
            }
        }

        $manageBuilder = new ManageBuilder('services', 'state');
        $manageBuilder = $this->state_manage_builder($manageBuilder);
        $manageBuilder->build();

        $possibleTransitions = $reservation->getSM()->getPossibleTransitions();

        if (
            $this->_isPost()
            && $this->_validateToken()
            && in_array($this->_getString('transition'), $possibleTransitions, true)
        ) {
            $response = new AjaxFormResponse();
            $postData = $this->request->getPost();

            if ($manageBuilder->isValid($postData)) {
                $formData = $manageBuilder->getData();
                $transition = $this->_getString('transition');

                if ($result = $reservationsRepo->changeStatus($reservation, $transition) and $result === true) {
                    if (isset($formData['comment'])) {
                        $reservation->getStateObject()->comment = $formData['comment'];
                    }

                    if (isset($formData['visible_to_user'])) {
                        $reservation->getStateObject()->visible_to_user = $formData['visible_to_user'];
                    }

                    Registry::load('Administrators.Log.logic')->log('change reservation status', [
                        'item' => $reservation,
                        'transition' => $transition
                    ]);

                    DoctrineOrm::$entityManager->flush();

                    if ($isIframe) {
                        //$response->runJScallback('parent.jQuery.fancybox.close();parent.window.location.reload();');
                    } else {
                        $response->redirectTo($this->getListingAction());
                    }
                } else {
                    if (is_string($result)) {
                        $this->_error('literal:' . $result);
                    } else {
                        $this->_error('there is errors in form');
                    }
                }
            } else {
                $response->setErrors($manageBuilder->getValidationErrors(true));
                $this->_error('there is errors in form');
            }

            $response->gatherMessages();

            return $this->_json($response);
        }

        $possibleTransitionsList = array_map(function (string $possibleTransition) {
            return [
                'id' => $possibleTransition,
                'title' => ServicesReservationsUtilities::formatTransitionName($possibleTransition)
            ];
        }, $possibleTransitions);

        $this->_set('item', $reservation);
        $this->_set('possibleTransitions', $possibleTransitionsList);
        $this->_set('manageBuilder', $manageBuilder);
        $this->_set('backAction', $this->getListingAction());

        $this->addClientScript('form-ajax');

        if ($isIframe) {
            $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);
        }

        return $this->_render();
    }

    protected function state_manage_builder(ManageBuilder $manageBuilder)
    {
        $manageBuilder->addField('state', [
            'type' => 'smarty_eval',
            'eval' => '<h3>{$item->getStatusTitle()}</h3>'
        ]);
        $manageBuilder->addField('comment', [
            'type' => ManageBuilder::TYPE_TEXTAREA
        ]);
        $manageBuilder->addField('visible_to_user', [
            'type' => ManageBuilder::TYPE_CHECKBOX,
            'label' => 'applications:state visible_to_user',
            'default_value' => true
        ]);

        return $manageBuilder;
    }
    //endregion States


    //region DayOff. Nedarbo dienos
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-calendar-times-o, priority=10
    public function listing_dayoff()
    {
        $this->_getMainItem();
        return $this->listing('dayOff');
    }

    //@fwXmlConfig permission=write
    public function add_dayoff()
    {
        $link = mb_strtolower($this->request->getModuleKey()) . "/edit_dayoff";
        if ($item = $this->_getMainItem()) {
            $link .= '/item_id.' . $item->getId();
        }
        $this->_redirect($link);
    }

    //@fwXmlConfig permission=edit
    public function edit_dayoff()
    {
        $this->_getMainItem();
        $this->addClientscript('datepicker');
        return $this->edit('dayOff');
    }

    //@fwXmlConfig permission=delete
    public function delete_dayoff()
    {
        return $this->delete('dayOff');
    }

    /**
     * Add record lists' columns
     *
     * @param ListingBuilder $builder
     * @return ListingBuilder
     */
    protected function dayoff_listing_builder(ListingBuilder $builder): ListingBuilder
    {
        if ($this->mainItem) {
            //$builder->setActionParam("item_id=" . $this->mainItem->getId());
            $builder->addActionsParam('item_id', $this->mainItem->getId());
            $builder->setPagerLinkParam("item_id." . $this->mainItem->getId());
        }

        $builder->removeFormAction(ListingBuilder::$FORM_POSITION);
        $builder->removeAction(ListingBuilder::$ACTION_ORDERABLE);
        $builder->addAction(ListingBuilder::$ACTION_TED, [
            'orderable' => ["field" => "visible"],
            "th_class" => "x3",
            "skip_toggle" => true
        ]);

        $builder->addSettingField('time_from', ['date_format' => 'H:i']);
        $builder->addSettingField('time_to', ['date_format' => 'H:i']);

        return $builder
            /*
            ->addField('service', [
                'eval' => '{if !empty($item->service)}{$item->service->get("title")}{else}-{/if}',
                'orderable' => false
            ])
            */
            ->addField('date', [
                'date_format' => 'Y-m-d',
                'orderable' => [
                    'default_field' => true,
                    'default_direction' => 'DESC'
                ]
            ])
            ->addField('weekday', [
                'eval' => '{if !empty($item->weekday)}{$localization->get("global week day {$item->weekday}")}{/if}',
                'orderable' => true
            ])
            ->addField('time', [
                'orderable' => [
                    'field' => 'time_from'
                ],
                'eval' => '{$item->getTimeRange()}'
            ])
            /*
            ->addFilter('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Services.Item',
                'loadValues' => ['repository' => 'Services.Item', 'method' => 'getList'],
            ])
            ->addFilter('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Datasets.City', 'method' => 'getList']
            ])
            */


            // Add city field (not filter)
            ->addField('city', [
                'eval' => '{$item->getCityTitle()}',
                'orderable' => true
            ])

        ;
    }

    /**
     * @param ManageBuilder $builder
     * @return ManageBuilder
     */
    protected function dayoff_manage_builder(ManageBuilder $builder): ManageBuilder
    {
        $serviceValue = null;
        if ($this->mainItem) {
            $serviceValue = $this->mainItem->getId();
        }

        return $builder
            /*
            ->addField('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Services.Item',
                'loadValues' => ['repository' => 'Services.Item', 'method' => 'getList'],
                'value' => $serviceValue,
            ])
            */
            ->addField('type', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.DayOff', 'method' => 'getTypesList'],
                'default_value' => 'day',
                'validation' => 'not_blank',
            ])
            ->addField("date", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d'),
                'validation' => 'not_blank',
                'nullable' => true,
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'type',
                        'eq' => 'day'
                    ]
                ],
            ])
            ->addField('weekday', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'loadValues' => ['repository' => 'Services.DayOff', 'method' => 'getWeekdaysList'],
                'validation' => 'not_blank',
                'conditionals' => [
                    [
                        'show' => true,
                        'when' => 'type',
                        'eq' => 'weekday'
                    ]
                ],
            ])
            ->addField("time_from", [
                'type' => ManageBuilder::TYPE_TIME,
                'input_options' => [
                    'stepMinute' => 5
                ],
                'col' => 'col-sm-6'
            ])
            ->addField("time_to", [
                'type' => ManageBuilder::TYPE_TIME,
                'input_options' => [
                    'stepMinute' => 5
                ],
                'col' => 'col-sm-6'
            ])


            // Add city selector
            ->addField('city', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Datasets.City',
                'loadValues' => ['repository' => 'Datasets.City', 'method' => 'getList'],
                'id' => 'city'
            ])
        ;
    }

    protected function _setFilterDayoff($filter)
    {
        if ($this->mainItem) {
            $filter['service'] = $this->mainItem->getId();
        }

        return $filter;
    }
    //endregion DayOff. Nedarbo dienos


    //region Period
    //@fwXmlConfig permission=read
    public function listing_period()
    {
        if ($assocData = $this->getAssociationParentData()) {
            $service = $assocData->getEntity();
        }

        if (empty($service)) {
            $this->_error('services: missing service');

            if ($this->_isIframe()) {
                $this->setMainTemplate(GeneralTemplates::TEMPLATE_IFRAME);
            } elseif ($this->_isAjax()) {
                $this->setMainTemplate(GeneralTemplates::TEMPLATE_AJAX);
            }

            return '';
        }

        return $this->listing('period');
    }

    //@fwXmlConfig permission=write
    public function add_period()
    {
        parent::add('period');
    }

    //@fwXmlConfig permission=edit
    public function edit_period()
    {
        $this->addClientscript('maskedinput');

        return parent::edit('period');
    }

    //@fwXmlConfig permission=edit
    public function toggle_period()
    {
        return parent::toggle("period");
    }

    //@fwXmlConfig permission=delete
    public function delete_period()
    {
        return parent::delete('period');
    }

    /**
     * Add record lists' columns
     *
     * @param ListingBuilder $builder
     * @return ListingBuilder
     */
    protected function period_listing_builder(ListingBuilder $builder): ListingBuilder
    {
        $builder->removeFormAction(ListingBuilder::$FORM_POSITION);
        $builder->removeAction(ListingBuilder::$ACTION_ORDERABLE);

        return $builder
            ->addField('register_from', [
                'date_format' => 'Y-m-d',
                'orderable' => [
                    'default_field' => true,
                    'default_direction' => 'DESC'
                ]
            ])
            ->addField('register_to', [
                'date_format' => 'Y-m-d'
            ])
            ->addField('period_from', [
                'date_format' => 'Y-m-d'
            ])
            ->addField('period_to', [
                'date_format' => 'Y-m-d'
            ])
        ;
    }

    /**
     * @param ManageBuilder $manageBuilder
     * @return ManageBuilder
     */
    protected function period_manage_builder(ManageBuilder $manageBuilder): ManageBuilder
    {
        if (!($manageBuilder->entity instanceof ServicesPeriod && $manageBuilder->entity->getId())) {
            /*
            $manageBuilder->addField('service', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'entity' => 'Services.Item',
                'loadValues' => ['repository' => 'Services.Item', 'method' => 'getList'],
                'validation' => 'not_blank'
            ]);
            */
        }

        return $manageBuilder
            ->addTab('business_hours', ['icon' => 'fa fa-clock-o'])
            ->addField("register_from", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d'),
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField("register_to", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d', strtotime('+1 month')),
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField("period_from", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d'),
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField("period_to", [
                'type' => ManageBuilder::TYPE_DATE,
                'default_value' => date('Y-m-d', strtotime('+1 month')),
                'validation' => 'not_blank',
                'col' => 'col-sm-6'
            ])
            ->addField('business_hours', [
                'type' => ManageBuilder::TYPE_BLOCK,
                'parsable' => true,
                'render_label' => false,
                'load' => 'services.view',
                'action' => '_business_hours_block',
                'tab' => 'business_hours'
            ])
            ->addVisible();

    }

    public function _business_hours_block($parameters = [])
    {
        if (empty($parameters['target_item'])) {
            return '';
        }

        return $this->_render('services.business_hours.block.tpl');
    }

    protected function _setFilterPeriod($filter)
    {
        if ($this->mainItem) {
            $filter['service'] = $this->mainItem->getId();
        }
        $filter['order'] = 'item.register_from DESC';

        return $filter;
    }
    //endregion Period


    //region Certificate
    public function certificate()
    {
        if (
            $id = $this->_getInt('id')
            /** @var ServicesParticipant|null $participant */
            and $participant = Registry::loadRepository('Services.Participant')->getOne(['id' => $id])
        ) {
            Registry::load('Services.Certificate.logic')->logPdf($participant->reservation, $participant);
            Registry::load('Services.Certificate.logic')->generateCertificate($participant);
        }

        $this->_back();
    }
    //endregion Certificate


    //region Reports. Ataskaitos
    //@fwXmlConfig permission=read, menu=1, icon=fa fa-line-chart, priority=5
    public function listing_report()
    {
        $this->_itemName = 'reservation';

        $tab = $this->getReportTab();
        $fn = 'report' . ucfirst($tab);
        /** @see static::reportGeneral() */
        /** @see static::reportServices() */

        $this->_set('tabsList', $this->getReportsTabs());
        $this->_set('currentTab', $tab);

        if ($this->_get('ff', false) === false) {
            //Jeigu atėjo per meniu, tuomet uždėti default filtrą "Atlikta"
            $this->request->setParameter('filter', ['status' => RegistrationStates::STATE_COMPLETED]);
        }

        return $this->{$fn}();
    }

    protected function reportGeneral()
    {
        $reservationsRepo = Registry::loadRepository('Services.Reservation');

        /** @var DatasetsItemRepository $situationsRepo */
        $situationsRepo = Registry::loadRepository('Services.Situation');

        /** @var ServicesSituation[] $servicesSituations */
        $servicesSituations = $situationsRepo->getList();
        foreach ($servicesSituations as &$situation) {
            $filterParams = [
                /** @see ServicesReservationRepository::render_situation_filter() */
                'situation' => $situation->getId(),
            ];
            if ($this->_get('filter')) {
                $filterParams = array_merge($filterParams, $this->_get('filter'));
            }
            $stats = $reservationsRepo->getStats($filterParams);
            $situation->count_items = $stats['total_items'];
            $situation->count_persons = $stats['total_participants'];
        }
        unset($situation);

        /** @var DatasetsItemRepository $citiesRepo */
        $citiesRepo = Registry::loadRepository('Datasets.City');
        /** @var DatasetsCity[] $cities */
        $cities = $citiesRepo->getList();
        foreach ($cities as &$city) {
            $filterParams = [
                /** @see ServicesReservationRepository::render_city_filter() */
                'city' => $city->getId(),
            ];
            if ($this->_get('filter')) {
                $filterParams = array_merge($filterParams, $this->_get('filter'));
            }
            $stats = $reservationsRepo->getStats($filterParams);
            $city->count_items = $stats['total_items'];
            $city->count_persons = $stats['total_participants'];
        }
        unset($city);

        /** @var DatasetsItemRepository $citiesRepo */
        $classesRepo = Registry::loadRepository('Datasets.Class');
        /** @var DatasetsClass[] $cities */
        $classes = $classesRepo->getList();
        foreach ($classes as &$class) {
            $filterParams = [
                /** @see ServicesReservationRepository::render_class_filter() */
                'class' => $class->getId(),
            ];
            if ($this->_get('filter')) {
                $filterParams = array_merge($filterParams, $this->_get('filter'));
            }
            $stats = $reservationsRepo->getStats($filterParams);
            $class->count_items = $stats['total_items'];
            $class->count_persons = $stats['total_participants'];
        }
        unset($class);

        $statuses = $reservationsRepo->getStatusSelectList();
        foreach ($statuses as &$status) {
            $filterParams = [
                /** @see ServicesReservation::$status */
                'status' => $status['id'],
            ];
            if ($this->_get('filter')) {
                $filter = $this->_get('filter');
                unset($filter['status']);
                $filterParams = array_merge($filterParams, $filter);
            }
            $stats = $reservationsRepo->getStats($filterParams);
            $status['count_items'] = $stats['total_items'];
            $status['count_persons'] = $stats['total_participants'];
        }
        unset($status);


        $tables = [];
        $tables['Statistika pagal laboratoriją'] = [
            'headings' => [Registry::l10n()->get('services: laboratory'), 'Rezervacijų skaičius', 'Dalyvių skaičius'],
        ];
        foreach ($servicesSituations as $item) {
            /** @see static::reservation_listing_builder() */
            $url = $this->buildPopupUrl("services/listing_reservation?filter[situation]={$item->getId()}", null);
            $tables['Statistika pagal laboratoriją']['items'][] = [
                $item->title,
                $item->count_items ? "<a href='{$url}' data-fancybox data-type='iframe'>{$item->count_items}</a>" : '-',
                $item->count_persons ?: '-',
            ];
        }
        $tables['Rezervacijos pagal STEAM centrus'] = [
            'headings' => [Registry::l10n()->get('steam_center'), 'Rezervacijų skaičius', 'Dalyvių skaičius'],
        ];
        foreach ($cities as $item) {
            /** @see static::reservation_listing_builder() */
            $url = $this->buildPopupUrl("services/listing_reservation?filter[city]={$item->getId()}", null);
            $tables['Rezervacijos pagal STEAM centrus']['items'][] = [
                $item->title,
                $item->count_items ? "<a href='{$url}' data-fancybox data-type='iframe'>{$item->count_items}</a>" : '-',
                $item->count_persons ?: '-',
            ];
        }
        $tables['Rezervacijos pagal klases'] = [
            'headings' => [Registry::l10n()->get('class'), 'Rezervacijų skaičius', 'Dalyvių skaičius'],
        ];
        foreach ($classes as $item) {
            /** @see static::reservation_listing_builder() */
            $url = $this->buildPopupUrl("services/listing_reservation?filter[relations][Datasets.Class]={$item->getId()}", null);
            $tables['Rezervacijos pagal klases']['items'][] = [
                $item->title,
                $item->count_items ? "<a href='{$url}' data-fancybox data-type='iframe'>{$item->count_items}</a>" : '-',
                $item->count_persons ?: '-',
            ];
        }
        $tables['Rezervacijos pagal būseną'] = [
            'headings' => [Registry::l10n()->get('status'), 'Rezervacijų skaičius', 'Dalyvių skaičius'],
        ];
        foreach ($statuses as $item) {
            $filter = Registry::request()->getParameter('filter') ?: [];
            $filter = array_diff_key($filter, ['status' => null]);

            /** @see static::reservation_listing_builder() */
            $url = $this->buildPopupUrl("services/listing_reservation?filter[status]={$item['id']}", $filter);
            $tables['Rezervacijos pagal būseną']['items'][] = [
                $item['title'],
                $item['count_items'] ? "<a href='{$url}' data-fancybox data-type='iframe'>{$item['count_items']}</a>" : '-',
                $item['count_persons'] ?: '-',
            ];
        }

        $this->_set('tables', $tables);


        $listingBuilder = new ListingBuilder('Services', 'Reservation');
        $listingBuilder->useDefaultActions();
        $listingBuilder->useDefaultFormActions();
        $listingBuilder->useDefaultSettingsFields();
        $listingBuilder->addActionsParam('tab', 'general');
        $listingBuilder->addFilter('year', [
            /** @see ServicesReservationRepository::render_year_filter() */
            'type' => ManageBuilder::TYPE_SELECT2,
            'values' => Registry::loadRepository('Services.Reservation')->getVisitYearsForFilter(),
            'label' => 'literal:Veiklos metai',
        ]);
        $listingBuilder->addFilter('visit_time', [
            /** @see ServicesReservationRepository::render_visit_time_filter() */
            'type' => ManageBuilder::TYPE_DATERANGE,
            'label' => 'visit date',
        ]);
        $listingBuilder->addFilter('status', [
            /** @see ServicesReservation::$status */
            'type' => ManageBuilder::TYPE_SELECT2,
            'values' => $reservationsRepo->getStatusSelectList(),
        ]);
        $this->addExportActionToListingBuilder($listingBuilder, 'Services' . '.' . ucfirst('Reservation'));//Prideda export į xmlsx/xml
        $listingBuilder->disableSettingsFields();
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_ADD);
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_DELETE);
        $listingBuilder->removeFormAction('listingExport_xml');
        $listingBuilder->build();
        $this->_set('listingBuilder', $listingBuilder);
        $this->_set('filter', Registry::request()->getParameter('filter') ?: []);

        if (
            Registry::request()->has('export')
            and $format = Registry::request()->getString('export')
            and $format === 'xlsx'
        ) {
            $logic = $this->getExportLogic();
            /** @see ServicesReservationExportLogic */
            $logic->doExport($tables, $format, Registry::l10n()->get('services: stats ' . $this->getReportTab()));
        }

        return $this->_render('services.report.general.tpl');
    }

    protected function reportServices()
    {
        $reservationsRepo = Registry::loadRepository('Services.Reservation');
        $servicesRepo = Registry::loadRepository('Services.Item');

        $services = $servicesRepo->getList(['order' => 'item.position ASC, item.title ASC, item.id ASC']);
        foreach ($services as &$service) {
            $filterParams = [
                /** @see ServicesReservationRepository::render_class_filter() */
                'service' => $service->getId(),
            ];
            if ($this->_get('filter')) {
                $filterParams = array_merge($filterParams, $this->_get('filter'));
            }
            $stats = $reservationsRepo->getStats($filterParams);
            $service->count_items = $stats['total_items'];
            $service->count_persons = $stats['total_participants'];
        }
        unset($service);

        $tables = [];
        $tables['Statistika pagal veiklą'] = [
            'headings' => [Registry::l10n()->get('service'), Registry::l10n()->get('steam_center'), Registry::l10n()->get('situation'), 'Rezervacijų skaičius', 'Dalyvių skaičius'],
        ];
        foreach ($services as $item) {
            /** @see static::reservation_listing_builder() */
            $url = $this->buildPopupUrl("services/listing_reservation?filter[service]={$item['id']}", null);
            $tables['Statistika pagal veiklą']['items'][] = [
                $item->title,
                $item->getCityTitle(),
                $item->getSituation() ? $item->getSituation()->get('title') : '-',
                $item->count_items ? "<a href='{$url}' data-fancybox data-type='iframe'>{$item->count_items}</a>" : '-',
                $item->count_persons ?: '-',
            ];
        }
        $this->_set('tables', $tables);


        $listingBuilder = new ListingBuilder('Services', 'Reservation');
        $listingBuilder->useDefaultActions();
        $listingBuilder->useDefaultFormActions();
        $listingBuilder->useDefaultSettingsFields();
        $listingBuilder->addActionsParam('tab', 'services');
        $listingBuilder->addFilter('year', [
            /** @see ServicesReservationRepository::render_year_filter() */
            'type' => ManageBuilder::TYPE_SELECT2,
            'values' => Registry::loadRepository('Services.Reservation')->getVisitYearsForFilter(),
            'label' => 'literal:Veiklos metai',
        ]);
        $listingBuilder->addFilter('visit_time', [
            /** @see ServicesReservationRepository::render_visit_time_filter() */
            'type' => ManageBuilder::TYPE_DATERANGE,
            'label' => 'visit date',
        ]);
        $listingBuilder->addFilter('status', [
            /** @see ServicesReservation::$status */
            'type' => ManageBuilder::TYPE_SELECT2,
            'values' => $reservationsRepo->getStatusSelectList(),
        ]);
        $this->addExportActionToListingBuilder($listingBuilder, 'Services' . '.' . ucfirst('Reservation'));//Prideda export į xmlsx/xml
        $listingBuilder->disableSettingsFields();
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_ADD);
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_DELETE);
        $listingBuilder->removeFormAction('listingExport_xml');
        $listingBuilder->build();
        $this->_set('listingBuilder', $listingBuilder);
        $this->_set('filter', Registry::request()->getParameter('filter') ?: []);

        if (
            Registry::request()->has('export')
            and $format = Registry::request()->getString('export')
            and $format === 'xlsx'
        ) {
            $logic = $this->getExportLogic();
            /** @see ServicesReservationExportLogic */
            $logic->doExport($tables, $format, Registry::l10n()->get('services: stats ' . $this->getReportTab()));
        }

        return $this->_render('services.report.services.tpl');
    }








    // new report
    protected function reportEvents()
    {
        $reservationsRepo = Registry::loadRepository('Services.Reservation');
        $servicesRepo = Registry::loadRepository('Services.Item');
        $classesRepo = Registry::loadRepository('Datasets.Class');
        $classes = $classesRepo->getList();

        $services = $servicesRepo->getList(['order' => 'item.position ASC, item.title ASC, item.id ASC']);

        $serviceMap = [];
        foreach ($services as $service) {
            $serviceMap[$service->getId()] = $service;
        }

        $tables = [];
        $tables['Statistika pagal veiklas'] = [
            'headings' => [
                Registry::l10n()->get('service'),      // Veikla
                // Registry::l10n()->get('steam_center'),  // STEAM centras
                // Registry::l10n()->get('situation'),    // Laboratorija
                Registry::l10n()->get('visit date'),   // Vizito laikas
                Registry::l10n()->get('class'),        // Klasė
                'Dalyvių skaičius'                     //
            ],
            'items' => []
        ];

        foreach ($services as $service) {
            $serviceId = $service->getId();

            foreach ($classes as $class) {
                $classId = $class->getId();

                $filterParams = [
                    'service' => $serviceId,
                    'class' => $classId
                ];

                if ($this->_get('filter')) {
                    $filterParams = array_merge($filterParams, $this->_get('filter'));
                }

                $reservations = $reservationsRepo->getList($filterParams);

                // Group by visit date AND time
                $dateTimeGroups = [];

                foreach ($reservations as $reservation) {
                    $visitDate = null;
                    $visitTime = null;
                    $visitDateTime = null;

                    if (isset($reservation->visit_time)) {
                        $visitTimeRaw = $reservation->visit_time;

                        if (is_object($visitTimeRaw) && method_exists($visitTimeRaw, 'format')) {
                            $visitDate = $visitTimeRaw->format('Y-m-d');
                            $visitTime = $visitTimeRaw->format('H:i');
                            $visitDateTime = $visitTimeRaw->format('Y-m-d H:i');
                        }

                        elseif (is_string($visitTimeRaw)) {
                            try {
                                $date = new \DateTime($visitTimeRaw);
                                $visitDate = $date->format('Y-m-d');
                                $visitTime = $date->format('H:i');
                                $visitDateTime = $date->format('Y-m-d H:i');
                            } catch (\Exception $e) {
                                $visitDate = $visitTimeRaw;
                                $visitDateTime = $visitTimeRaw;
                            }
                        }
                    }

                    if (!$visitDate) {
                        if (method_exists($reservation, 'getVisitDate')) {
                            $visitDateObj = $reservation->getVisitDate();
                            if (is_object($visitDateObj) && method_exists($visitDateObj, 'format')) {
                                $visitDate = $visitDateObj->format('Y-m-d');
                                $visitTime = $visitDateObj->format('H:i');
                                $visitDateTime = $visitDateObj->format('Y-m-d H:i');
                            } else {
                                $visitDate = $visitDateObj;
                                $visitDateTime = $visitDateObj;
                            }
                        } elseif (method_exists($reservation, 'getVisitDateString')) {
                            $visitDate = $reservation->getVisitDateString();
                            $visitDateTime = $visitDate;
                        } elseif (method_exists($reservation, 'getVisitDay')) {
                            $visitDate = $reservation->getVisitDay();
                            $visitDateTime = $visitDate;
                        }
                    }

                    if (!$visitDate) {
                        continue;
                    }


                    if (!$visitTime && isset($reservation->visit_time_start)) {
                        $visitTime = $reservation->visit_time_start;
                        if (is_object($visitTime) && method_exists($visitTime, 'format')) {
                            $visitTime = $visitTime->format('H:i');
                        }

                        $visitDateTime = $visitDate . ' ' . $visitTime;
                    }

                    $groupKey = $visitDateTime ?: $visitDate;

                    $participantCount = 0;

                    if (isset($reservation->participants_count)) {
                        $participantCount = $reservation->participants_count;
                    } elseif (isset($reservation->participants)) {
                        if (is_array($reservation->participants)) {
                            $participantCount = count($reservation->participants);
                        } elseif (is_numeric($reservation->participants)) {
                            $participantCount = $reservation->participants;
                        }
                    }

                    if (!$participantCount) {
                        if (method_exists($reservation, 'getParticipantsCount')) {
                            $participantCount = $reservation->getParticipantsCount();
                        } elseif (method_exists($reservation, 'countParticipants')) {
                            $participantCount = $reservation->countParticipants();
                        }
                    }

                    if (!isset($dateTimeGroups[$groupKey])) {
                        $dateTimeGroups[$groupKey] = [
                            'count' => 0,
                            'participants' => 0,
                            'date' => $visitDate,  // Store just the date for URL filtering
                        ];
                    }

                    $dateTimeGroups[$groupKey]['count']++;
                    $dateTimeGroups[$groupKey]['participants'] += $participantCount;
                }

                foreach ($dateTimeGroups as $visitDateTime => $stats) {
                    if ($stats['count'] > 0) {
                        $urlParams = "filter[service]={$serviceId}&filter[relations][Datasets.Class]={$classId}&filter[visit_time][from]={$stats['date']}&filter[visit_time][to]={$stats['date']}";
                        $url = $this->buildPopupUrl("services/listing_reservation?{$urlParams}", null);

                        $participantsUrl = $this->buildPopupUrl("services/listing_reservation?{$urlParams}", null);

                        $tables['Statistika pagal veiklas']['items'][] = [
                            $service->title,
                            // $service->getCityTitle() ?: '-',
                            // $service->getSituation() ? $service->getSituation()->get('title') : '-',
                            $visitDateTime,  // Show the full date+time
                            $class->title,
                            "<a href='{$participantsUrl}' data-fancybox data-type='iframe'>{$stats['participants']}</a>" ?: '-',
                        ];
                    }
                }
            }
        }

        $collator = null;
        if (class_exists('Collator')) {
            $collator = new \Collator('lt_LT');
        }

        usort($tables['Statistika pagal veiklas']['items'], function ($a, $b) use ($collator) {
            if ($collator) {
                // First compare by service name (column 0)
                $serviceCompare = $collator->compare($a[0], $b[0]);
                if ($serviceCompare !== 0) {
                    return $serviceCompare;
                }

                return $collator->compare($a[1], $b[1]);
            } else {
                $serviceA = iconv('UTF-8', 'ASCII//TRANSLIT', $a[0]);
                $serviceB = iconv('UTF-8', 'ASCII//TRANSLIT', $b[0]);

                $serviceCompare = strcmp($serviceA, $serviceB);
                if ($serviceCompare !== 0) {
                    return $serviceCompare;
                }

                return strcmp($a[1], $b[1]);
            }
        });

        $this->_set('tables', $tables);

        $listingBuilder = new ListingBuilder('Services', 'Reservation');
        $listingBuilder->useDefaultActions();
        $listingBuilder->useDefaultFormActions();
        $listingBuilder->useDefaultSettingsFields();
        $listingBuilder->addActionsParam('tab', 'events');
        // $listingBuilder->addFilter('year', [
        //     'type' => ManageBuilder::TYPE_SELECT2,
        //     'values' => Registry::loadRepository('Services.Reservation')->getVisitYearsForFilter(),
        //     'label' => 'literal:Veiklos metai',
        // ]);
        // $listingBuilder->addFilter('visit_time', [
        //     'type' => ManageBuilder::TYPE_DATERANGE,
        //     'label' => 'visit date',
        // ]);
        // $listingBuilder->addFilter('status', [
        //     'type' => ManageBuilder::TYPE_SELECT2,
        //     'values' => $reservationsRepo->getStatusSelectList(),
        // ]);
        // $listingBuilder->addFilter('class', [
        //     'type' => ManageBuilder::TYPE_SELECT2,
        //     'values' => array_map(function ($class) {
        //         return [
        //             'id' => $class->getId(),
        //             'title' => $class->title
        //         ];
        //     }, $classes),
        //     'label' => 'class',
        // ]);
        $this->addExportActionToListingBuilder($listingBuilder, 'Services' . '.' . ucfirst('Reservation'));
        $listingBuilder->disableSettingsFields();
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_ADD);
        $listingBuilder->removeFormAction(ListingBuilder::$FORM_DELETE);
        $listingBuilder->removeFormAction('listingExport_xml');
        $listingBuilder->build();

        $this->_set('listingBuilder', $listingBuilder);
        $this->_set('filter', Registry::request()->getParameter('filter') ?: []);


        if (
            Registry::request()->has('export')
            and $format = Registry::request()->getString('export')
            and $format === 'xlsx'
        ) {
            $logic = $this->getExportLogic();
            
            // Sanitize table names for Excel (remove invalid characters)
            $sanitizedTables = [];
            foreach ($tables as $name => $data) {
                // Replace invalid Excel sheet characters: * : / \ ? [ ]
                $sanitizedName = str_replace(['*', ':', '/', '\\', '?', '[', ']'], '-', $name);
                // Limit length if needed (Excel has a 31 character limit for sheet names)
                if (mb_strlen($sanitizedName) > 31) {
                    $sanitizedName = mb_substr($sanitizedName, 0, 31);
                }
                $sanitizedTables[$sanitizedName] = $data;
            }
            
            /** @see ServicesReservationExportLogic */
            $logic->doExport($sanitizedTables, $format, $this->sanitizeExcelSheetName(Registry::l10n()->get('services: stats ' . $this->getReportTab())));
        }







        return $this->_render('services.report.events.tpl');
    }











    /**
     * Sanitizes a string for use as an Excel sheet name
     * 
     * @param string $name The name to sanitize
     * @return string The sanitized name
     */
    private function sanitizeExcelSheetName($name)
    {
        // Replace invalid Excel sheet characters: * : / \ ? [ ]
        $sanitized = str_replace(['*', ':', '/', '\\', '?', '[', ']'], '-', $name);

        // Limit length if needed (Excel has a 31 character limit for sheet names)
        if (mb_strlen($sanitized) > 31) {
            $sanitized = mb_substr($sanitized, 0, 31);
        }

        return $sanitized;
    }








    protected function buildPopupUrl(string $url, ?array $filter = [])
    {
        $filter ??= Registry::request()->getParameter('filter');

        if ($filter) {
            foreach ($filter as $key1 => $val1) {
                if (is_string($val1)) {
                    $url .= "&filter[{$key1}]={$val1}";
                } elseif (is_array($val1)) {
                    foreach ($val1 as $key2 => $val2) {
                        $url .= "&filter[{$key1}][{$key2}]={$val2}";
                    }
                }
            }
        }
        $url .= "&ff=1&_display=iframe";
        return $url;
    }

    protected function getReportsTabs()
    {
        return [
            'general' => [
                'id' => 'general',
                'i18n_title' => 'services: stats general',
            ],
            'services' => [
                'id' => 'services',
                'i18n_title' => 'services: stats services',
            ],


            // new report
            'events' => [
                'id' => 'events',
                'i18n_title' => 'services: stats events',
            ],

        ];
    }

    protected function getReportTab()
    {
        $tabsKeys = array_keys($this->getReportsTabs());

        if ($tab = $this->_getString('tab') and in_array($tab, $tabsKeys)) {
            return $tab;
        } else {
            return reset($tabsKeys);
        }
    }
    //endregion Reports. Ataskaitos


    //region Helpers
    public function closefancybox()
    {
        $reload = '';
        if (Registry::session()->get('reload_after')) {
            Registry::session()->delete('reload_after');
            $reload = 'parent.window.location.reload();';
        }
        $this->setMainTemplate(GeneralTemplates::TEMPLATE_POPUP);
        return '<script>parent.jQuery.fancybox.close();' . $reload . '</script>';
    }

    /**
     * Nustatom grazinimo duomenis po duomenu issaugojimo
     *
     * @param array $response
     * @param BaseEntity $entity
     */
    protected function setAfterEditResponse(AjaxFormResponse $response, BaseEntity $entity)
    {
        parent::setAfterEditResponse($response, $entity);

        if (
            $this->_getString('action') === ManageBuilder::FORM_SAVE_AND_CLOSE
            and $this->_isIframe()
            and Registry::session()->get('reload_after')
            and $callback = $response->getValue('callback')
        ) {
            Registry::session()->delete('reload_after');

            $response->runJScallback($callback . ';parent.window.location.reload();');
        }
    }

    /**
     * Grazina listing nuoroda
     *
     * @param bool $module
     *
     * @return string
     */
    protected function getListingAction($module = true)
    {
        $listingAction = parent::getListingAction($module);

        if (
            Registry::session()->get('reload_after')
            && (
                Registry::load('General.view')->getMainTemplate() == GeneralTemplates::TEMPLATE_IFRAME
                || $this->_isIframe()
            )
        ) {
            $listingAction .= ';parent.window.location.reload();';
        }

        return $listingAction;
    }

    /**
     * Patikrina ar perduotas item_id
     *
     * @return null|object
     */
    protected function _getMainItem()
    {
        if (isset($this->mainItem)) {
            return $this->mainItem;
        }

        if ($id = $this->_getInt('item_id') and $item = Registry::loadRepository('services.item')->find($id)) {
            $this->mainItem = $item;
            return $this->mainItem;
        } elseif (
            $this->_getString('parent_name') === 'service' and $id = $this->_getInt('id')
            and $item = Registry::loadRepository('services.item')->find($id)
        ) {
            $this->mainItem = $item;
            return $this->mainItem;
        }

        return null;
    }
    //endregion
}
