$(document).ready(function () {

    // console.log('hello from admin.js');


    //region Services modulis

    //puslapyje '/admin/services/listing_reservation' mygtuk<PERSON> paspaud<PERSON>, kad mokinys dalyvavo
    $('.services-change-participant-has_arrived').on('click', function (e) {
        var btn = $(this);
        var participantId = btn.attr('data-id');

        $.post({
            url: 'services/change_participant_has_arrived',
            //datatype: 'json',//xml, json, script, text, html
            //async: false
            data: {
                participant_id: participantId,
            },
            //beforeSend: function (jqXHR, settings) {},
            success: function (data) {
                //var json = $.parseJSON(data);
                //console.log(json);
                location.reload();
            },
            //error: function (jqXHR, textStatus, errorThrown) {
            //    console.error(jqXHR, textStatus, errorThrown);
            //},
            //complete: function (jqXHR, textStatus) {}
        });
    });

    //region '/admin/services/calendar'
    if ($('.toggle_event_date').length) {
        function changeStatus() {
            var btn = $('.toggle_event_date');
            if (+btn.attr('data-isinactive')) {
                btn.find('> span').text(btn.attr('data-l10n-on'));
            } else {
                btn.find('> span').text(btn.attr('data-l10n-off'));
            }
        }
        changeStatus();

        //kalendoriuje '/admin/services/calendar' popup'e paspaudė "išjungti/įjungti veiklą"
        $('.toggle_event_date').on('click', function (e) {
            var btn = $(this);
            var id = btn.attr('data-id');
            var datetime = btn.attr('data-datetime');

            $.post({
                url: 'services/toggle_event_date',
                //datatype: 'json',//xml, json, script, text, html
                //async: false
                data: {
                    id: id,
                    datetime: btn.attr('data-datetime'),
                },
                beforeSend: function (jqXHR, settings) {
                    btn.find('.spinner-border').show();
                    btn.attr('disabled', 'disabled')
                },
                success: function (data) {
                    if (data.status === 'disabled') {
                        btn.attr('data-isinactive', 1);
                    } else if (data.status === 'activated') {
                        btn.attr('data-isinactive', 0);
                    }

                    //region Pakeisti pagrindiniame lange kalendoriuje renginio teksto spalvą
                    var mainWindow = $('body', window.parent.document);
                    var mainModal = $('body');
                    var date = datetime.substr(0, 10);
                    var time = datetime.substr(-5);
                    mainWindow.find(`td[data-date="${date}"] a.fc-event`).filter(function () {
                        return this.fcSeg.eventRange.def.publicId == id
                            && $(this).find('.fc-event-time')[0].textContent === time;
                    }).each(function (index, element) {
                        if (data.status === 'disabled') {
                            $(element).addClass('is-inactive');
                        } else if (data.status === 'activated') {
                            $(element).removeClass('is-inactive');
                        }
                    })
                    //endregion
                },
                //error: function (jqXHR, textStatus, errorThrown) {
                //    console.error(jqXHR, textStatus, errorThrown);
                //},
                complete: function (jqXHR, textStatus) {
                    btn.removeAttr('disabled')
                    btn.find('.spinner-border').hide();
                    changeStatus();

                }
            });
        });
    }
    //endregion /admin/services/calendar

    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
    //endregion Services modulis




});












// /admin/services/edit_event add "Select all" option to custom_service select

function addSelectAllOption() {
    // Wait a moment for Select2 to be fully initialized
    setTimeout(function() {
      // Try to find the element by ID first
      const customService = document.getElementById('custom_service');
      
      // If element doesn't exist or isn't a select, exit
      if (!customService || customService.tagName !== 'SELECT') {
        // console.log('Select element not found or not ready yet');
        return;
      }
      
      const $select = $(customService);
      
      // Check if it's actually a Select2
      if (!$select.data('select2')) {
        // console.log('Select2 not initialized on this element yet, will try again later');
        setTimeout(addSelectAllOption, 500); // Try again in 500ms
        return;
      }
      
      // Get currently selected values
      const selectedValues = $select.val() || [];
      
      // Check if there are already items selected
      if (selectedValues.length > 0) {
        // console.log('Select2 already has selected items, skipping "Select All" option');
        return;
      }
      
      // Check if Select All option already exists to avoid duplicates
      if (!Array.from(customService.options).some(option => option.value === 'select-all')) {
        // console.log('Adding Select All option...');
        
        // Create and add the Select All option directly to the DOM element
        const selectAllOption = document.createElement('option');
        selectAllOption.value = 'select-all';
        selectAllOption.text = 'Pasirinkite visus';
        customService.insertBefore(selectAllOption, customService.firstChild);
        
        // Track if we're in the process of updating to avoid infinite loops
        let isUpdating = false;
        
        // Remove any existing event handlers to avoid duplicates
        $select.off('select2:select.selectAll select2:unselect.selectAll');
        
        // Handle the Select All functionality with namespaced events
        $select.on('select2:select.selectAll select2:unselect.selectAll', function(e) {
          if (isUpdating) return;
          isUpdating = true;
          
          try {
            const values = $(this).val() || [];
            
            // If "Select All" was selected
            if (e.params && e.params.data && e.params.data.id === 'select-all') {
              if (values.includes('select-all')) {
                // Select all options
                const allOptionValues = Array.from(customService.options)
                  .map(option => option.value);
                $(this).val(allOptionValues).trigger('change');
              } else {
                // Deselect all options
                $(this).val([]).trigger('change');
              }
            } 
            // If a regular option was selected/deselected
            else {
              // Check if all options except "Select All" are selected
              const allOptionsExceptAll = Array.from(customService.options)
                .filter(option => option.value !== 'select-all')
                .map(option => option.value);
              
              const allSelected = allOptionsExceptAll.every(value => 
                values.includes(value)
              );
              
              // Update "Select All" accordingly
              if (allSelected && !values.includes('select-all')) {
                values.push('select-all');
                $(this).val(values).trigger('change');
              } else if (!allSelected && values.includes('select-all')) {
                const newValues = values.filter(val => val !== 'select-all');
                $(this).val(newValues).trigger('change');
              }
            }
          } finally {
            isUpdating = false;
          }
        });
        
        // Refresh the Select2 to show our new option
        $select.trigger('change');
        // console.log('Successfully added "Select All" option');
      } else {
        // console.log('"Select All" option already exists');
      }
    }, 300); // Initial delay to ensure Select2 is initialized
  }
  
  // Initial call when document is ready
  $(document).ready(function() {
    addSelectAllOption();
  });
  
  // Also call our function when the page content might have changed
  $(document).on('ajaxComplete', function() {
    addSelectAllOption();
  });
  
  // Also try on Select2's own events
  $(document).on('select2:open', function(e) {
    // Wait a bit to ensure the dropdown is fully open
    setTimeout(function() {
      // Only call if the opened Select2 is our target
      if (e.target.id === 'custom_service') {
        addSelectAllOption();
      }
    }, 100);
  });

// Auto-select all services when situation changes for "by_lab" mode
$(document).ready(function() {
    // Function to auto-select all services when they are loaded
    function autoSelectAllServices() {
        const $servicesField = $('#services');
        const $byLabField = $('input[name="by_lab"]:checked');

        // Only proceed if we're in "by_lab" mode (value = 1) and field has auto_select_all option
        if ($byLabField.length && $byLabField.val() === '1' && $servicesField.length) {
            // Check if the field has auto_select_all option
            const options = $servicesField.data('options');
            if (options && options.auto_select_all) {
                // Wait for options to be loaded via AJAX, then select all
                const checkForOptions = setInterval(function() {
                    const $options = $servicesField.find('option');
                    if ($options.length > 1) { // More than just the empty option
                        clearInterval(checkForOptions);

                        // Get all option values except empty ones
                        const allValues = [];
                        $options.each(function() {
                            if ($(this).val() && $(this).val() !== '') {
                                allValues.push($(this).val());
                            }
                        });

                        if (allValues.length > 0) {
                            $servicesField.val(allValues).trigger('change');
                        }
                    }
                }, 200);

                // Stop checking after 10 seconds
                setTimeout(function() {
                    clearInterval(checkForOptions);
                }, 10000);
            }
        }
    }

    // Listen for changes in situation field
    $(document).on('change', '#service_situation', function() {
        setTimeout(autoSelectAllServices, 500);
    });

    // Listen for changes in by_lab radio buttons
    $(document).on('change', 'input[name="by_lab"]', function() {
        if ($(this).val() === '1') {
            setTimeout(autoSelectAllServices, 1000);
        }
    });

    // Also check on page load if by_lab is already set
    setTimeout(function() {
        const $byLabField = $('input[name="by_lab"]:checked');
        if ($byLabField.length && $byLabField.val() === '1') {
            autoSelectAllServices();
        }
    }, 1500);
});